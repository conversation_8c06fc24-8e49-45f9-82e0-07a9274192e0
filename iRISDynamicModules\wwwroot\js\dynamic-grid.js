/**
 * Dynamic Kendo Grid Helper for iRISDynamicModules
 * Provides responsive grid functionality with dynamic height and always-visible pagination
 */

var DynamicGrid = (function() {
    'use strict';

    // Configuration object
    var config = {
        minHeight: 300,
        maxHeightRatio: 0.8,
        footerHeight: 60,
        resizeDebounce: 250,
        orientationChangeDelay: 300,
        adjustmentDelay: 500
    };

    // Utility functions
    var utils = {
        // Debounce function to limit resize event frequency
        debounce: function(func, wait) {
            var timeout;
            return function executedFunction() {
                var context = this;
                var args = arguments;
                var later = function() {
                    timeout = null;
                    func.apply(context, args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // Check if device is mobile
        isMobile: function() {
            return $(window).width() < 768;
        },

        // Check if device is in portrait mode
        isPortrait: function() {
            return $(window).height() > $(window).width();
        },

        // Get screen size category
        getScreenSize: function() {
            var width = $(window).width();
            if (width < 576) return 'xs';
            if (width < 768) return 'sm';
            if (width < 992) return 'md';
            if (width < 1200) return 'lg';
            return 'xl';
        }
    };

    // Height calculation functions
    var heightCalculator = {
        // Calculate optimal grid height based on screen size and available space
        calculateGridHeight: function() {
            var windowHeight = $(window).height();
            var containerOffset = $('.dynamic-grid-container').offset();
            var headerOffset = containerOffset ? containerOffset.top : 200;
            
            // Calculate available height
            var availableHeight = windowHeight - headerOffset - config.footerHeight;
            
            // Apply responsive adjustments
            var screenSize = utils.getScreenSize();
            var adjustment = this.getHeightAdjustment(screenSize);
            availableHeight -= adjustment;
            
            // Set bounds
            var maxHeight = windowHeight * config.maxHeightRatio;
            return Math.max(config.minHeight, Math.min(availableHeight, maxHeight));
        },

        // Get height adjustment based on screen size
        getHeightAdjustment: function(screenSize) {
            var adjustments = {
                'xs': 50,
                'sm': 40,
                'md': 30,
                'lg': 20,
                'xl': 10
            };
            return adjustments[screenSize] || 20;
        }
    };

    // Pagination configuration functions
    var paginationConfig = {
        // Get optimal page size based on screen height
        getOptimalPageSize: function() {
            var screenHeight = $(window).height();
            var screenSize = utils.getScreenSize();
            
            if (screenSize === 'xs') return 15;
            if (screenSize === 'sm') return 25;
            if (screenHeight < 600) return 25;
            if (screenHeight < 800) return 50;
            if (screenHeight < 1200) return 75;
            return 100;
        },

        // Get page size options based on device type
        getPageSizeOptions: function() {
            var isMobile = utils.isMobile();
            return isMobile ? [10, 25, 50] : [10, 25, 50, 100, 200, 500];
        },

        // Get button count for pagination based on screen width
        getButtonCount: function() {
            var screenSize = utils.getScreenSize();
            var buttonCounts = {
                'xs': 3,
                'sm': 5,
                'md': 7,
                'lg': 9,
                'xl': 11
            };
            return buttonCounts[screenSize] || 7;
        }
    };

    // Grid adjustment functions
    var gridAdjuster = {
        // Adjust grid content height to ensure pagination is always visible
        adjustGridContentHeight: function(gridSelector) {
            gridSelector = gridSelector || "#grid";
            var grid = $(gridSelector).data("kendoGrid");
            if (!grid) return;
            
            var gridElement = grid.element;
            var toolbar = gridElement.find('.k-grid-toolbar');
            var header = gridElement.find('.k-grid-header');
            var pager = gridElement.find('.k-grid-pager');
            var content = gridElement.find('.k-grid-content');
            
            var containerHeight = $('.dynamic-grid-container').height();
            var toolbarHeight = toolbar.outerHeight() || 0;
            var headerHeight = header.outerHeight() || 0;
            var pagerHeight = pager.outerHeight() || 0;
            var padding = 10;
            
            var contentHeight = containerHeight - toolbarHeight - headerHeight - pagerHeight - padding;
            content.height(Math.max(200, contentHeight));
        },

        // Ensure pagination is properly positioned and styled
        ensurePaginationVisibility: function(grid) {
            var pager = grid.pager;
            if (pager) {
                pager.element.show();
                pager.element.css({
                    'position': 'sticky',
                    'bottom': '0',
                    'z-index': '30',
                    'background': '#f8f9fa',
                    'border-top': '2px solid #dee2e6',
                    'box-shadow': '0 -2px 4px rgba(0,0,0,0.1)'
                });
            }
        },

        // Update grid for responsive changes
        updateGridResponsive: function(gridSelector) {
            gridSelector = gridSelector || "#grid";
            var grid = $(gridSelector).data("kendoGrid");
            if (!grid) return;

            var newHeight = heightCalculator.calculateGridHeight();
            grid.element.height(newHeight);
            grid.resize();
            this.adjustGridContentHeight(gridSelector);
            
            // Update pagination for new screen size
            var pager = grid.pager;
            if (pager) {
                pager.options.buttonCount = paginationConfig.getButtonCount();
                pager.refresh();
            }
        }
    };

    // Event handlers
    var eventHandlers = {
        // Handle window resize with debouncing
        handleResize: utils.debounce(function() {
            gridAdjuster.updateGridResponsive();
        }, config.resizeDebounce),

        // Handle orientation change for mobile devices
        handleOrientationChange: function() {
            setTimeout(function() {
                gridAdjuster.updateGridResponsive();
            }, config.orientationChangeDelay);
        },

        // Handle grid data bound event
        handleDataBound: function(e) {
            var grid = e.sender;
            gridAdjuster.ensurePaginationVisibility(grid);
            
            // Adjust content height after data is loaded
            setTimeout(function() {
                gridAdjuster.adjustGridContentHeight();
            }, 100);
        },

        // Handle grid resize event
        handleGridResize: function(e) {
            setTimeout(function() {
                gridAdjuster.adjustGridContentHeight();
            }, 100);
        }
    };

    // Public API
    return {
        // Initialize dynamic grid functionality
        init: function(gridSelector, options) {
            gridSelector = gridSelector || "#grid";
            options = options || {};
            
            // Merge custom config
            if (options.config) {
                $.extend(config, options.config);
            }
            
            // Set up event listeners
            $(window).resize(eventHandlers.handleResize);
            $(window).on('orientationchange', eventHandlers.handleOrientationChange);
            
            // Initial adjustment
            setTimeout(function() {
                gridAdjuster.adjustGridContentHeight(gridSelector);
            }, config.adjustmentDelay);
        },

        // Get configuration for grid initialization
        getGridConfig: function() {
            return {
                height: heightCalculator.calculateGridHeight(),
                pageSize: paginationConfig.getOptimalPageSize(),
                pageSizes: paginationConfig.getPageSizeOptions(),
                buttonCount: paginationConfig.getButtonCount(),
                dataBound: eventHandlers.handleDataBound,
                resize: eventHandlers.handleGridResize
            };
        },

        // Manually trigger grid adjustment
        adjustGrid: function(gridSelector) {
            gridAdjuster.updateGridResponsive(gridSelector);
        },

        // Get current screen information
        getScreenInfo: function() {
            return {
                size: utils.getScreenSize(),
                isMobile: utils.isMobile(),
                isPortrait: utils.isPortrait(),
                width: $(window).width(),
                height: $(window).height()
            };
        },

        // Update configuration
        updateConfig: function(newConfig) {
            $.extend(config, newConfig);
        }
    };
})();

// Auto-initialize if jQuery is available
$(document).ready(function() {
    if (typeof $ !== 'undefined' && $('.dynamic-grid-container').length > 0) {
        DynamicGrid.init();
    }
});
