@using Kendo.Mvc.UI
@inject IHttpContextAccessor HttpContextAccessor
@using Microsoft.AspNetCore.Http
@{
    ViewData["Title"] = TempData["ModuleName"];
    ViewBag.Title = TempData["ModuleName"];
    ViewBag.pTitle = TempData["ModuleName"];
    Layout = "~/Views/_Shared/_Layout_tagingstation.cshtml";
    var session = HttpContextAccessor.HttpContext.Session;
    var appurl = session.GetString("BaseUrl");
    var gridDataSet = ViewBag.gridData;
}

@await Html.PartialAsync("~/Views/_Shared/_shared_template_scripts.cshtml")

<!-- Include dynamic grid CSS and JS -->
<link rel="stylesheet" href="~/css/dynamic-grid.css" />
<script src="~/js/dynamic-grid.js"></script>

<style>
    .k-spacer {
        display: none;
    }

    .k-footer-template {
        display: none;
    }
</style>

<div class="dynamic-grid-container">
    <div class="grid-wrapper">
        <div id="grid"></div>
    </div>
</div>
<script>
    var appurl='@appurl';
    kendo.ui.progress($("#grid"), true);

    $(document).ready(function () {
        var gridData = @Html.Raw(gridDataSet);

                function cleanDataKeys(data) {
        return data.map(item => {
            let newItem = {};
            for (let key in item) {
                if (item.hasOwnProperty(key)) {
                    let newKey = cleanFieldName(key)
                    let val = item[key];

                    if (
                        key.toLowerCase().includes("date") &&
                        typeof val === "string" &&
                        !isNaN(Date.parse(val))
                    ) {
                        newItem[newKey] = new Date(val);
                    } else {
                        newItem[newKey] = val;
                    }
                }
            }
            return newItem;
        });
    }



        gridData.rows = cleanDataKeys(gridData.rows);

        const modelFields = Object.fromEntries(
            gridData.columns.map(col => [
               cleanFieldName (col),
                { type: col.toLowerCase().includes("date") ? "date" : "string" }
            ])
        );

        const columns = gridData.columns.filter(col => cleanFieldName(col).toLowerCase() !== "reportlink")
            .map(col => {
                const isDateColumn = col.toLowerCase().includes("date");
                const cleanCol = cleanFieldName(col);

                return {
                    field: cleanCol,
                    title: col,
                    template: function (dataItem) {
                        if (cleanFieldName(col).toLowerCase() == "reportname") {
                            return `<a href='${dataItem.ReportLink}' target='_blank' style='color:blue;'>${dataItem[cleanCol]}</a>`;
                        }
                        else if (isDateColumn) {
                          return `<span>${kendo.toString(dataItem[cleanCol], "MM/dd/yyyy hh:mm:ss tt")}</span>`;
                        }
                        else {
                            return `<span>${dataItem[cleanCol]}</span>`;
                        }
                    },
                    width: 200,
                    groupHeaderTemplate: `${col}: #= kendo.toString(value, "${isDateColumn ? 'MM/dd/yyyy hh:mm:ss tt' : ''}") # (Count: #= count #)`,
                    aggregates: ["count"],
                    format: isDateColumn ? "{0:MM/dd/yyyy hh:mm tt}" : undefined,
                    filterable: isDateColumn
                        ? {
                            ui: function (element) {
                                element.kendoDateTimePicker({
                                    format: "MM/dd/yyyy hh:mm tt"
                                });
                            }
                        }
                        : true
                };
            });


        // Get dynamic grid configuration
        var gridConfig = DynamicGrid.getGridConfig();

        // Initialize the grid with enhanced responsive configuration
        $("#grid").kendoGrid({
            dataSource: {
                data: gridData.rows,
                schema: {
                    model: {
                        fields: modelFields
                    }
                },
                pageSize: gridConfig.pageSize,
                aggregate: gridData.columns.map(c => ({ field: cleanFieldName(c), aggregate: "count" }))
            },
            columnMenu: {
                filterable: true,
                sortable: true,
                columns: true
            },
            toolbar: ["search", "excel", "pdf"],
            pageable: {
                pageSizes: gridConfig.pageSizes,
                refresh: true,
                pageSizes: true,
                buttonCount: gridConfig.buttonCount,
                info: true,
                input: true,
                numeric: true
            },
            height: gridConfig.height,
            scrollable: {
                virtual: false,
                endless: false
            },
            sortable: {
                mode: "multiple",
                allowUnsort: true
            },
            groupable: {
                messages: {
                    empty: "Drag column headers here to group by that column"
                }
            },
            columns: columns,
            filterable: {
                mode: "row",
                extra: false
            },
            navigatable: true,
            resizable: true,
            reorderable: true,
            serverOperations: false,
            dataBound: gridConfig.dataBound,
            resize: gridConfig.resize
        });

        // Initialize dynamic grid functionality
        DynamicGrid.init("#grid", {
            config: {
                minHeight: 250,
                maxHeightRatio: 0.85,
                footerHeight: 50
            }
        });

        kendo.ui.progress($("#grid"), false);
    });
        function cleanFieldName(name) {
        return name
            .trim()
            .replace(/[^a-zA-Z0-9_$]/g, '')
                .replace(/^[0-9]+/, '');
        }

</script>
