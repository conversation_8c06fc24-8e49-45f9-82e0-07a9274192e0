/* Dynamic Kendo Grid Styles for iRISDynamicModules */

/* Main container for dynamic height grid */
.dynamic-grid-container {
    height: calc(100vh - 200px);
    min-height: 400px;
    display: flex;
    flex-direction: column;
    position: relative;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Grid wrapper for proper flex layout */
.grid-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
}

/* Main grid styling */
#grid {
    height: 100%;
    display: flex;
    flex-direction: column;
    border: none;
    border-radius: 8px;
    overflow: hidden;
}

/* Grid header - fixed at top */
#grid .k-grid-header {
    flex-shrink: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    border-bottom: 2px solid #5a67d8;
    position: sticky;
    top: 0;
    z-index: 20;
}

#grid .k-grid-header th {
    background: transparent;
    color: white;
    border-color: rgba(255,255,255,0.2);
    padding: 12px 8px;
    font-size: 13px;
}

/* Grid toolbar - fixed below header */
#grid .k-grid-toolbar {
    flex-shrink: 0;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 12px;
    position: sticky;
    top: 0;
    z-index: 15;
}

/* Grid content area - scrollable */
#grid .k-grid-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: auto;
    position: relative;
}

/* Grid rows styling */
#grid .k-grid-content tr {
    transition: background-color 0.2s ease;
}

#grid .k-grid-content tr:hover {
    background-color: #f8f9fa;
}

#grid .k-grid-content tr.k-alt {
    background-color: #fafbfc;
}

#grid .k-grid-content td {
    padding: 10px 8px;
    border-bottom: 1px solid #e9ecef;
    font-size: 13px;
    vertical-align: middle;
}

/* Grid pagination - fixed at bottom */
#grid .k-grid-pager {
    flex-shrink: 0;
    position: sticky;
    bottom: 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 2px solid #dee2e6;
    padding: 12px;
    z-index: 30;
    box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
}

/* Pagination controls styling */
.k-grid-pager .k-pager-nav,
.k-grid-pager .k-pager-numbers .k-link {
    border-radius: 4px;
    margin: 0 2px;
    transition: all 0.2s ease;
}

.k-grid-pager .k-pager-nav:hover,
.k-grid-pager .k-pager-numbers .k-link:hover {
    background-color: #667eea;
    color: white;
    transform: translateY(-1px);
}

/* Responsive Design */

/* Large screens (desktops) */
@media (min-width: 1200px) {
    .dynamic-grid-container {
        height: calc(100vh - 180px);
        min-height: 500px;
    }
}

/* Medium screens (tablets) */
@media (max-width: 992px) {
    .dynamic-grid-container {
        height: calc(100vh - 160px);
        min-height: 400px;
    }
    
    #grid .k-grid-content table {
        min-width: 800px; /* Enable horizontal scrolling */
    }
    
    #grid .k-grid-header th,
    #grid .k-grid-content td {
        padding: 8px 6px;
        font-size: 12px;
    }
}

/* Small screens (mobile landscape) */
@media (max-width: 768px) {
    .dynamic-grid-container {
        height: calc(100vh - 140px);
        min-height: 350px;
    }
    
    #grid .k-grid-toolbar {
        padding: 8px;
    }
    
    #grid .k-grid-toolbar .k-button {
        margin: 2px;
        padding: 6px 10px;
        font-size: 12px;
    }
    
    #grid .k-grid-pager {
        padding: 8px;
        font-size: 12px;
    }
    
    /* Hide some pagination elements on small screens */
    .k-grid-pager .k-pager-sizes {
        display: none;
    }
}

/* Extra small screens (mobile portrait) */
@media (max-width: 576px) {
    .dynamic-grid-container {
        height: calc(100vh - 120px);
        min-height: 300px;
        margin: 0 -15px; /* Extend to screen edges */
        border-radius: 0;
    }
    
    #grid {
        border-radius: 0;
    }
    
    #grid .k-grid-header th,
    #grid .k-grid-content td {
        padding: 6px 4px;
        font-size: 11px;
    }
    
    #grid .k-grid-toolbar .k-button {
        padding: 4px 8px;
        font-size: 11px;
        margin: 1px;
    }
    
    /* Stack toolbar buttons vertically if needed */
    #grid .k-grid-toolbar {
        flex-wrap: wrap;
    }
    
    /* Simplify pagination for mobile */
    .k-grid-pager .k-pager-info {
        display: none;
    }
    
    .k-grid-pager .k-pager-numbers {
        display: flex;
        justify-content: center;
    }
}

/* Loading indicator */
.k-loading-mask {
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 1000;
}

.k-loading-image {
    background-image: url('data:image/gif;base64,R0lGODlhEAAQAPIAAP///wAAAMLCwkJCQgAAAGJiYoKCgpKSkiH/C05FVFNDQVBFMi4wAwEAAAAh/hpDcmVhdGVkIHdpdGggYWpheGxvYWQuaW5mbwAh+QQJCgAAACwAAAAAEAAQAAADMwi63P4wyklrE2MIOggZnAdOmGYJRbExwroUmcG2LmDEwnHQLVsYOd2mBzkYDAdKa+dIAAAh+QQJCgAAACwAAAAAEAAQAAADNAi63P5OjCEgG4QMu7DmikRxQlFUYDEZIGBMRVsaqHwctXXf7WEYB4Ag1xjihkMZsiUkKhIAIfkECQoAAAAsAAAAABAAEAAAAzYIujIjK8pByJDMlFYvBoVjHA70GU7xSUJhmKtwHPAKzLO9HMaoKwJZ7Rf8AYPDDzKpZBqfvwQAIfkECQoAAAAsAAAAABAAEAAAAzMIumIlK8oyhpHsnFZfhYumCYUhDAQxRIdhHBGqRoKw0R8DYlJd8z0fMDgsGo/IpHI5TAAAIfkECQoAAAAsAAAAABAAEAAAAzIIunInK0rnZBTwGPNMgQwmdsNgXGJUlIWEuR5oWUIpz8pAEAMe6TwfwyYsGo/IpFKSAAAh+QQJCgAAACwAAAAAEAAQAAADMwi6IMKQORfjdOe82p4wGccc4CEuQradylesojEMBgsUc2G7sDX3lQGBMLAJibufbSlKAAAh+QQJCgAAACwAAAAAEAAQAAADMgi63P7wjRLEuQRNnGt7QpVdNhHJBkaK0VGJQCdHjyOhZQTrOKNdx8JAFsM2VqvXqwEAIfkECQoAAAAsAAAAABAAEAAAAzQIujIjK8pByJDMlFYvBoVjHA70GU7xSUJhmKtwHPAKzLO9HMaoKwJZ7Rf8AYPDDzKpZBqfvwQAIfkECQoAAAAsAAAAABAAEAAAAzMIumIlK8oyhpHsnFZfhYumCYUhDAQxRIdhHBGqRoKw0R8DYlJd8z0fMDgsGo/IpHI5TAAAIfkECQoAAAAsAAAAABAAEAAAAzIIunInK0rnZBTwGPNMgQwmdsNgXGJUlIWEuR5oWUIpz8pAEAMe6TwfwyYsGo/IpFKSAAAh+QQJCgAAACwAAAAAEAAQAAADMwi6IMKQORfjdOe82p4wGccc4CEuQradylesojEMBgsUc2G7sDX3lQGBMLAJibufbSlKAAAh+QQJCgAAACwAAAAAEAAQAAADMgi63P7wjRLEuQRNnGt7QpVdNhHJBkaK0VGJQCdHjyOhZQTrOKNdx8JAFsM2VqvXqwEAOw==');
}

/* Custom scrollbar for webkit browsers */
#grid .k-grid-content::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

#grid .k-grid-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

#grid .k-grid-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

#grid .k-grid-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Ensure grid maintains aspect ratio on orientation change */
@media screen and (orientation: landscape) {
    .dynamic-grid-container {
        height: calc(100vh - 120px);
    }
}

@media screen and (orientation: portrait) {
    .dynamic-grid-container {
        height: calc(100vh - 160px);
    }
}

/* Print styles */
@media print {
    .dynamic-grid-container {
        height: auto !important;
        min-height: auto !important;
    }
    
    #grid .k-grid-content {
        overflow: visible !important;
    }
    
    #grid .k-grid-pager {
        display: none !important;
    }
}
