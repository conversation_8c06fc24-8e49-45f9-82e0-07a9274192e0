html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
}

/* Enhanced Dynamic Grid Styles for iRISDynamicModules */
.dynamic-grid-container {
    width: 100%;
    max-width: 100%;
    overflow: hidden;
}

/* Ensure grid is responsive on all devices */
.k-grid {
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Mobile-first responsive design */
@media (max-width: 576px) {
    .k-grid-toolbar .k-button {
        margin: 2px;
        padding: 4px 8px;
        font-size: 12px;
    }

    .k-grid-pager {
        font-size: 12px;
    }

    .k-grid-pager .k-pager-sizes {
        display: none; /* Hide page sizes on very small screens */
    }
}

@media (max-width: 768px) {
    .k-grid-header th {
        font-size: 12px;
        padding: 8px 4px;
    }

    .k-grid td {
        font-size: 12px;
        padding: 6px 4px;
    }
}

/* Ensure pagination is always visible and properly styled */
.k-grid-pager {
    background-color: #f8f9fa;
    border-top: 2px solid #dee2e6;
    padding: 10px;
    position: sticky;
    bottom: 0;
    z-index: 100;
}

/* Improve grid content scrolling */
.k-grid-content {
    overflow-x: auto;
    overflow-y: auto;
}

/* Better toolbar styling */
.k-grid-toolbar {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 10px;
}

/* Responsive column handling */
@media (max-width: 992px) {
    .k-grid .k-grid-content table {
        min-width: 800px; /* Ensure minimum width for horizontal scrolling */
    }
}