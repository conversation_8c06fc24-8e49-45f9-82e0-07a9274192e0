{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "Default": "Data Source=MAILPTP90;Initial Catalog=MAT_SUP_CCH1CV_SUPPLYTRACEDB;User ID=sa;Password=*********;"
  },
  "LogFileInfo": {
    "FilePath": "D:\\Serilog",
    "LogFileName": "iRISupplyWebDesk.log",
    "Active": "NO"
  },

  "ADSettings": {
    "LOGIN_TYPE": "2", //Takes Values 0(Only Db Login), 1(Only AD Login),2(Both AD and Db),3(KeyCloak)
    "AD_BASE_URI": "http://localhost:9898/api/v1/",
    "AD_REQUEST_URI": "iRISupply/Login/"
    //"AD_REQUEST_URI": "/iRISupplyServices/api/values/"
  },
  "BaseUrl": {
    "URL": ""
  },
  "WebAppBaseUrl": {
    "WebURL": "https://localhost:7259"
  },
  "MVCURL": {
    "URL": "http://localhost/IrisWebmvc/"
  },
  "KeycloakAuthentication": {
    "OpenIdConnect": {
      "ClientId": "auth_login",
      "ClientSecret": "sINQGJh69jrhYUXfeLYJoEwvhpLv67CN",
      "Authority": "https://t2server:9443/realms/irisupplyhub"
    }
  }
}
