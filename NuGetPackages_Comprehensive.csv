"Sl No","Package Name","Dll Name","Version","Link to NuGet","Project Name","Used For","Remarks","Type"
"1","GemBox.Spreadsheet","GemBox.Spreadsheet.dll","2025.6.110","https://www.nuget.org/packages/GemBox.Spreadsheet","iRISupplyWebDeskTaggingStation","Excel file processing and manipulation","","Direct NuGet Reference"
"2","GoogleAuthenticator","GoogleAuthenticator.dll","3.2.0","https://www.nuget.org/packages/GoogleAuthenticator","iRISupplyWebDeskAuthLogin","Two-factor authentication with Google Authenticator","","Direct NuGet Reference"
"3","Humanizer.Core","Humanizer.dll","2.14.1","https://www.nuget.org/packages/Humanizer.Core","Multiple (Transitive)","Human-friendly string transformations and formatting","Transitive dependency via Telerik UI (Microsoft.CodeAnalysis)","Transitive Dependency"
"4","JsonDiffPatch.Net","JsonDiffPatch.Net.dll","2.3.0","https://www.nuget.org/packages/JsonDiffPatch.Net","iRISuppyWebDeskUserActivityLog","JSON comparison and patching","","Direct NuGet Reference"
"5","Microsoft.AspNetCore.Authentication.OpenIdConnect","Microsoft.AspNetCore.Authentication.OpenIdConnect.dll","6.0.36","https://www.nuget.org/packages/Microsoft.AspNetCore.Authentication.OpenIdConnect","iRISupplyWebDeskAuthLogin","OpenID Connect authentication","","Direct NuGet Reference"
"6","Microsoft.AspNetCore.Authentication.OpenIdConnect","Microsoft.AspNetCore.Authentication.OpenIdConnect.dll","8.0.18","https://www.nuget.org/packages/Microsoft.AspNetCore.Authentication.OpenIdConnect","iRISupplyWebDesk","OpenID Connect authentication","","Direct NuGet Reference"
"7","Microsoft.AspNetCore.Components.Web","Microsoft.AspNetCore.Components.Web.dll","6.0.0","https://www.nuget.org/packages/Microsoft.AspNetCore.Components.Web","iRISupplyWebDeskConstants","Blazor web components","","Direct NuGet Reference"
"8","Microsoft.AspNetCore.SignalR","Microsoft.AspNetCore.SignalR.dll","1.1.0","https://www.nuget.org/packages/Microsoft.AspNetCore.SignalR","iRISupplyWebDeskHubs","Real-time web communication","","Direct NuGet Reference"
"9","Microsoft.CodeAnalysis","Microsoft.CodeAnalysis.dll","4.4.0","https://www.nuget.org/packages/Microsoft.CodeAnalysis","Multiple (Transitive)","C# and VB.NET code analysis and compilation","Transitive dependency via Telerik UI","Transitive Dependency"
"10","Microsoft.CodeAnalysis.CSharp","Microsoft.CodeAnalysis.CSharp.dll","4.4.0","https://www.nuget.org/packages/Microsoft.CodeAnalysis.CSharp","Multiple (Transitive)","C# code analysis and compilation","Transitive dependency via Telerik UI","Transitive Dependency"
"11","Microsoft.CodeAnalysis.CSharp.Workspaces","Microsoft.CodeAnalysis.CSharp.Workspaces.dll","4.4.0","https://www.nuget.org/packages/Microsoft.CodeAnalysis.CSharp.Workspaces","Multiple (Transitive)","C# workspace APIs for code analysis","Transitive dependency via Telerik UI","Transitive Dependency"
"12","Microsoft.CodeAnalysis.VisualBasic","Microsoft.CodeAnalysis.VisualBasic.dll","4.4.0","https://www.nuget.org/packages/Microsoft.CodeAnalysis.VisualBasic","Multiple (Transitive)","VB.NET code analysis and compilation","Transitive dependency via Telerik UI","Transitive Dependency"
"13","Microsoft.CodeAnalysis.VisualBasic.Workspaces","Microsoft.CodeAnalysis.VisualBasic.Workspaces.dll","4.4.0","https://www.nuget.org/packages/Microsoft.CodeAnalysis.VisualBasic.Workspaces","Multiple (Transitive)","VB.NET workspace APIs for code analysis","Transitive dependency via Telerik UI","Transitive Dependency"
"14","Microsoft.CodeAnalysis.Workspaces.Common","Microsoft.CodeAnalysis.Workspaces.dll","4.4.0","https://www.nuget.org/packages/Microsoft.CodeAnalysis.Workspaces.Common","Multiple (Transitive)","Code analysis workspace APIs","Transitive dependency via Telerik UI","Transitive Dependency"
"15","Microsoft.IdentityModel.Protocols.OpenIdConnect","Microsoft.IdentityModel.Protocols.OpenIdConnect.dll","8.12.1","https://www.nuget.org/packages/Microsoft.IdentityModel.Protocols.OpenIdConnect","iRISupplyWebDesk","OpenID Connect protocol support","","Direct NuGet Reference"
"16","Microsoft.IdentityModel.Tokens","Microsoft.IdentityModel.Tokens.dll","8.12.1","https://www.nuget.org/packages/Microsoft.IdentityModel.Tokens","iRISupplyWebDesk, iRISupplyWebDeskAuthLogin","Security token handling","","Direct NuGet Reference"
"17","Newtonsoft.Json","Newtonsoft.Json.dll","13.0.3","https://www.nuget.org/packages/Newtonsoft.Json","iRISupplyWebDesk, iRISupplyWebDeskAuthLogin, iRISupplyWebDeskCommon, JSONLogger, ManageModules","JSON serialization and deserialization","","Direct NuGet Reference"
"18","QRCoder","QRCoder.dll","Unknown","https://www.nuget.org/packages/QRCoder","Multiple (Transitive)","QR code generation library","Transitive dependency - version needs verification","Transitive Dependency"
"19","RabbitMQ.Client","RabbitMQ.Client.dll","6.7.0","https://www.nuget.org/packages/RabbitMQ.Client","iRISupplyWebDesk, iRISupplyWebDeskCommon, iRISupplyWebDeskHubs","Message queuing and communication","","Direct NuGet Reference"
"20","Serilog","Serilog.dll","3.1.1","https://www.nuget.org/packages/Serilog","iRISupplyWebDesk","Structured logging framework","","Direct NuGet Reference"
"21","Serilog.Enrichers.Process","Serilog.Enrichers.Process.dll","2.0.2","https://www.nuget.org/packages/Serilog.Enrichers.Process","ManageRoles","Process information enrichment for Serilog","","Direct NuGet Reference"
"22","Serilog.Enrichers.Thread","Serilog.Enrichers.Thread.dll","3.1.0","https://www.nuget.org/packages/Serilog.Enrichers.Thread","ManageRoles","Thread information enrichment for Serilog","","Direct NuGet Reference"
"23","Serilog.Extensions.Hosting","Serilog.Extensions.Hosting.dll","8.0.0","https://www.nuget.org/packages/Serilog.Extensions.Hosting","iRISupplyWebDesk","Serilog integration with .NET hosting","","Direct NuGet Reference"
"24","Serilog.Settings.Configuration","Serilog.Settings.Configuration.dll","8.0.0","https://www.nuget.org/packages/Serilog.Settings.Configuration","iRISupplyWebDesk","Configuration support for Serilog","","Direct NuGet Reference"
"25","Serilog.Sinks.File","Serilog.Sinks.File.dll","5.0.0","https://www.nuget.org/packages/Serilog.Sinks.File","iRISupplyWebDesk","File output for Serilog logging","","Direct NuGet Reference"
"26","SerilogTimings","SerilogTimings.dll","3.0.1","https://www.nuget.org/packages/SerilogTimings","ManageRoles","Performance timing for Serilog","","Direct NuGet Reference"
"27","SqlTableDependency","SqlTableDependency.dll","8.5.8","https://www.nuget.org/packages/SqlTableDependency","iRISupplyWebDesk","SQL Server table change notifications","","Direct NuGet Reference"
"28","System.Configuration.ConfigurationManager","System.Configuration.ConfigurationManager.dll","8.0.0","https://www.nuget.org/packages/System.Configuration.ConfigurationManager","iRISupplyWebDesk, ManageModules","Configuration management","","Direct NuGet Reference"
"29","System.Data.SqlClient","System.Data.SqlClient.dll","4.8.6","https://www.nuget.org/packages/System.Data.SqlClient","iRISupplyWebDeskCommon, iRISupplyWebDeskManageOrders, iRISupplyWebDeskManageOrdersDept, iRISupplyWebDeskManagePatients, iRISuppyWebDeskUserActivityLog, ManageModules","SQL Server database connectivity","","Direct NuGet Reference"
"30","System.Data.SqlClient","System.Data.SqlClient.dll","4.9.0","https://www.nuget.org/packages/System.Data.SqlClient","iRISDynamicModules","SQL Server database connectivity","","Direct NuGet Reference"
"31","System.IdentityModel.Tokens.Jwt","System.IdentityModel.Tokens.Jwt.dll","8.12.1","https://www.nuget.org/packages/System.IdentityModel.Tokens.Jwt","iRISupplyWebDesk, iRISupplyWebDeskAuthLogin","JWT token processing","","Direct NuGet Reference"
"32","System.Management","System.Management.dll","8.0.0","https://www.nuget.org/packages/System.Management","iRISupplyWebDeskAuthLogin","System management and WMI access","","Direct NuGet Reference"
"33","Telerik.UI.for.AspNet.Core","Telerik.UI.for.AspNet.Core.dll","2024.1.130","https://www.nuget.org/packages/Telerik.UI.for.AspNet.Core","iRISDynamicModules, iRISupplyWebDeskAuthLogin, iRISupplyWebDeskManageOrders, iRISupplyWebDeskManageOrdersDept, iRISupplyWebDeskManagePatients, ManageModules, ManageRoles","UI components and controls for ASP.NET Core","Older version - consider upgrading","Direct NuGet Reference"
"34","Telerik.UI.for.AspNet.Core","Telerik.UI.for.AspNet.Core.dll","2025.1.227","https://www.nuget.org/packages/Telerik.UI.for.AspNet.Core","iRISupplyHub.ManageProducts, iRISupplyWebDesk, iRISupplyWebDeskTaggingStation","UI components and controls for ASP.NET Core","Latest version","Direct NuGet Reference"
