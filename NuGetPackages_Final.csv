"Sl No","Package Name","Dll Name","Version","Link to NuGet","Project Name","Used For","Remarks"
"1","GemBox.Spreadsheet","GemBox.Spreadsheet.dll","2025.6.110","https://www.nuget.org/packages/GemBox.Spreadsheet","iRISupplyWebDeskTaggingStation","Excel file processing and manipulation",""
"2","GoogleAuthenticator","GoogleAuthenticator.dll","3.2.0","https://www.nuget.org/packages/GoogleAuthenticator","iRISupplyWebDeskAuthLogin","Two-factor authentication with Google Authenticator",""
"3","JsonDiffPatch.Net","JsonDiffPatch.Net.dll","2.3.0","https://www.nuget.org/packages/JsonDiffPatch.Net","iRISuppyWebDeskUserActivityLog","JSON comparison and patching",""
"4","Microsoft.AspNetCore.Authentication.OpenIdConnect","Microsoft.AspNetCore.Authentication.OpenIdConnect.dll","6.0.36","https://www.nuget.org/packages/Microsoft.AspNetCore.Authentication.OpenIdConnect","iRISupplyWebDeskAuthLogin","OpenID Connect authentication",""
"5","Microsoft.AspNetCore.Authentication.OpenIdConnect","Microsoft.AspNetCore.Authentication.OpenIdConnect.dll","8.0.18","https://www.nuget.org/packages/Microsoft.AspNetCore.Authentication.OpenIdConnect","iRISupplyWebDesk","OpenID Connect authentication",""
"6","Microsoft.AspNetCore.Components.Web","Microsoft.AspNetCore.Components.Web.dll","6.0.0","https://www.nuget.org/packages/Microsoft.AspNetCore.Components.Web","iRISupplyWebDeskConstants","Blazor web components",""
"7","Microsoft.AspNetCore.SignalR","Microsoft.AspNetCore.SignalR.dll","1.1.0","https://www.nuget.org/packages/Microsoft.AspNetCore.SignalR","iRISupplyWebDeskHubs","Real-time web communication",""
"8","Microsoft.IdentityModel.Protocols.OpenIdConnect","Microsoft.IdentityModel.Protocols.OpenIdConnect.dll","8.12.1","https://www.nuget.org/packages/Microsoft.IdentityModel.Protocols.OpenIdConnect","iRISupplyWebDesk","OpenID Connect protocol support",""
"9","Microsoft.IdentityModel.Tokens","Microsoft.IdentityModel.Tokens.dll","8.12.1","https://www.nuget.org/packages/Microsoft.IdentityModel.Tokens","iRISupplyWebDesk, iRISupplyWebDeskAuthLogin","Security token handling",""
"10","Newtonsoft.Json","Newtonsoft.Json.dll","13.0.3","https://www.nuget.org/packages/Newtonsoft.Json","iRISupplyWebDesk, iRISupplyWebDeskAuthLogin, iRISupplyWebDeskCommon, JSONLogger, ManageModules","JSON serialization and deserialization",""
"11","RabbitMQ.Client","RabbitMQ.Client.dll","6.7.0","https://www.nuget.org/packages/RabbitMQ.Client","iRISupplyWebDesk, iRISupplyWebDeskCommon, iRISupplyWebDeskHubs","Message queuing and communication",""
"12","Serilog","Serilog.dll","3.1.1","https://www.nuget.org/packages/Serilog","iRISupplyWebDesk","Structured logging framework",""
"13","Serilog.Enrichers.Process","Serilog.Enrichers.Process.dll","2.0.2","https://www.nuget.org/packages/Serilog.Enrichers.Process","ManageRoles","Process information enrichment for Serilog",""
"14","Serilog.Enrichers.Thread","Serilog.Enrichers.Thread.dll","3.1.0","https://www.nuget.org/packages/Serilog.Enrichers.Thread","ManageRoles","Thread information enrichment for Serilog",""
"15","Serilog.Extensions.Hosting","Serilog.Extensions.Hosting.dll","8.0.0","https://www.nuget.org/packages/Serilog.Extensions.Hosting","iRISupplyWebDesk","Serilog integration with .NET hosting",""
"16","Serilog.Settings.Configuration","Serilog.Settings.Configuration.dll","8.0.0","https://www.nuget.org/packages/Serilog.Settings.Configuration","iRISupplyWebDesk","Configuration support for Serilog",""
"17","Serilog.Sinks.File","Serilog.Sinks.File.dll","5.0.0","https://www.nuget.org/packages/Serilog.Sinks.File","iRISupplyWebDesk","File output for Serilog logging",""
"18","SerilogTimings","SerilogTimings.dll","3.0.1","https://www.nuget.org/packages/SerilogTimings","ManageRoles","Performance timing for Serilog",""
"19","SqlTableDependency","SqlTableDependency.dll","8.5.8","https://www.nuget.org/packages/SqlTableDependency","iRISupplyWebDesk","SQL Server table change notifications",""
"20","System.Configuration.ConfigurationManager","System.Configuration.ConfigurationManager.dll","8.0.0","https://www.nuget.org/packages/System.Configuration.ConfigurationManager","iRISupplyWebDesk, ManageModules","Configuration management",""
"21","System.Data.SqlClient","System.Data.SqlClient.dll","4.8.6","https://www.nuget.org/packages/System.Data.SqlClient","iRISupplyWebDeskCommon, iRISupplyWebDeskManageOrders, iRISupplyWebDeskManageOrdersDept, iRISupplyWebDeskManagePatients, iRISuppyWebDeskUserActivityLog, ManageModules","SQL Server database connectivity",""
"22","System.Data.SqlClient","System.Data.SqlClient.dll","4.9.0","https://www.nuget.org/packages/System.Data.SqlClient","iRISDynamicModules","SQL Server database connectivity",""
"23","System.IdentityModel.Tokens.Jwt","System.IdentityModel.Tokens.Jwt.dll","8.12.1","https://www.nuget.org/packages/System.IdentityModel.Tokens.Jwt","iRISupplyWebDesk, iRISupplyWebDeskAuthLogin","JWT token processing",""
"24","System.Management","System.Management.dll","8.0.0","https://www.nuget.org/packages/System.Management","iRISupplyWebDeskAuthLogin","System management and WMI access",""
"25","Telerik.UI.for.AspNet.Core","Telerik.UI.for.AspNet.Core.dll","2024.1.130","https://www.nuget.org/packages/Telerik.UI.for.AspNet.Core","iRISDynamicModules, iRISupplyWebDeskAuthLogin, iRISupplyWebDeskManageOrders, iRISupplyWebDeskManageOrdersDept, iRISupplyWebDeskManagePatients, ManageModules, ManageRoles","UI components and controls for ASP.NET Core","Older version - consider upgrading"
"26","Telerik.UI.for.AspNet.Core","Telerik.UI.for.AspNet.Core.dll","2025.1.227","https://www.nuget.org/packages/Telerik.UI.for.AspNet.Core","iRISupplyHub.ManageProducts, iRISupplyWebDesk, iRISupplyWebDeskTaggingStation","UI components and controls for ASP.NET Core","Latest version"
